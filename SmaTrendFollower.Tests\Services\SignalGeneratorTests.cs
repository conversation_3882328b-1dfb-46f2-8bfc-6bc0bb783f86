using SmaTrendFollower.Console.Extensions;
using SmaTrendFollower.Services;
using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

public class SignalGeneratorTests
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IUniverseProvider> _mockUniverseProvider;
    private readonly Mock<ILogger<SignalGenerator>> _mockLogger;
    private readonly SignalGenerator _signalGenerator;

    public SignalGeneratorTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockUniverseProvider = new Mock<IUniverseProvider>();
        _mockLogger = new Mock<ILogger<SignalGenerator>>();
        
        _signalGenerator = new SignalGenerator(
            _mockMarketDataService.Object,
            _mockUniverseProvider.Object,
            _mockLogger.Object);
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task RunAsync_WithValidSignals_AppliesVolatilityThrottle()
    {
        // Arrange
        var symbols = new[] { "LOW_VOL", "HIGH_VOL" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Create signals with different volatility levels
        // Low volatility: 1.5% ATR/Price ratio (should pass < 3%)
        var lowVolatilityBars = CreateBarsWithVolatility("LOW_VOL", price: 100m, volatilityPercent: 1.5m, isUptrend: true);

        // High volatility: 6% ATR/Price ratio (should be filtered out >= 3%)
        var highVolatilityBars = CreateBarsWithVolatility("HIGH_VOL", price: 100m, volatilityPercent: 6.0m, isUptrend: true);

        SetupMarketDataResponse("LOW_VOL", lowVolatilityBars);
        SetupMarketDataResponse("HIGH_VOL", highVolatilityBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert - Verify that volatility throttle is working
        signalList.Should().NotBeEmpty("At least some signals should pass the filters");

        // All returned signals should have ATR/Price < 3%
        foreach (var signal in signalList)
        {
            var atrRatio = signal.Atr / signal.Price;
            atrRatio.Should().BeLessThan(0.03m, $"Signal {signal.Symbol} should have ATR/Price < 3%, but was {atrRatio:P2}");
        }

        // Low volatility should be included
        signalList.Should().Contain(s => s.Symbol == "LOW_VOL", "Low volatility signal should pass the filter");
    }

    [Fact]
    public async Task RunAsync_WithHighVolatilitySignals_FiltersThemOut()
    {
        // Arrange
        var symbols = new[] { "VOLATILE1", "VOLATILE2" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Both signals have ATR/Price > 3%
        var highVolBars1 = CreateBarsWithVolatility("VOLATILE1", price: 100m, volatilityPercent: 5.0m, isUptrend: true);
        var highVolBars2 = CreateBarsWithVolatility("VOLATILE2", price: 100m, volatilityPercent: 4.5m, isUptrend: true);

        SetupMarketDataResponse("VOLATILE1", highVolBars1);
        SetupMarketDataResponse("VOLATILE2", highVolBars2);

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty(); // All signals filtered out due to high volatility
    }

    [Fact]
    public async Task RunAsync_WithSmaFiltering_OnlyIncludesUptrends()
    {
        // Arrange
        var symbols = new[] { "UPTREND", "DOWNTREND", "MIXED" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // UPTREND: price > sma50 > sma200 (should pass)
        var uptrendBars = CreateBarsWithVolatility("UPTREND", price: 100m, volatilityPercent: 2.0m, isUptrend: true);

        // DOWNTREND: price < sma50 < sma200 (should fail)
        var downtrendBars = CreateBarsWithVolatility("DOWNTREND", price: 100m, volatilityPercent: 2.0m, isUptrend: false);

        // MIXED: price > sma50 but price < sma200 (should fail) - create sideways trend
        var mixedBars = CreateBarsWithVolatility("MIXED", price: 100m, volatilityPercent: 2.0m, isUptrend: null);

        SetupMarketDataResponse("UPTREND", uptrendBars);
        SetupMarketDataResponse("DOWNTREND", downtrendBars);
        SetupMarketDataResponse("MIXED", mixedBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(1);
        signalList.Should().Contain(s => s.Symbol == "UPTREND");
        signalList.Should().NotContain(s => s.Symbol == "DOWNTREND");
        signalList.Should().NotContain(s => s.Symbol == "MIXED");
    }

    [Fact]
    public async Task RunAsync_RanksBySixMonthReturn_ReturnsTopPerformers()
    {
        // Arrange
        var symbols = new[] { "LOW_RETURN", "HIGH_RETURN", "MED_RETURN" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // All pass filters but have different returns - create with different trend strengths
        var lowReturnBars = CreateBarsWithReturn("LOW_RETURN", finalPrice: 105m, startPrice: 100m, volatilityPercent: 2.0m);  // 5% return
        var highReturnBars = CreateBarsWithReturn("HIGH_RETURN", finalPrice: 125m, startPrice: 100m, volatilityPercent: 2.0m); // 25% return
        var medReturnBars = CreateBarsWithReturn("MED_RETURN", finalPrice: 115m, startPrice: 100m, volatilityPercent: 2.0m);   // 15% return

        SetupMarketDataResponse("LOW_RETURN", lowReturnBars);
        SetupMarketDataResponse("HIGH_RETURN", highReturnBars);
        SetupMarketDataResponse("MED_RETURN", medReturnBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(3);
        signalList[0].Symbol.Should().Be("HIGH_RETURN"); // Highest return first
        signalList[1].Symbol.Should().Be("MED_RETURN");
        signalList[2].Symbol.Should().Be("LOW_RETURN");
    }

    [Fact]
    public async Task RunAsync_WithTopNLimit_ReturnsCorrectCount()
    {
        // Arrange
        var symbols = new[] { "STOCK1", "STOCK2", "STOCK3", "STOCK4", "STOCK5" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // All pass filters
        foreach (var symbol in symbols)
        {
            var bars = CreateBarsWithVolatility(symbol, price: 100m, volatilityPercent: 2.0m, isUptrend: true);
            SetupMarketDataResponse(symbol, bars);
        }

        // Act
        var signals = await _signalGenerator.RunAsync(3); // Limit to top 3

        // Assert
        signals.Should().HaveCount(3);
    }

    [Fact]
    public async Task RunAsync_WithInsufficientData_SkipsSymbol()
    {
        // Arrange
        var symbols = new[] { "GOOD_DATA", "BAD_DATA" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        var goodBars = CreateBarsWithVolatility("GOOD_DATA", price: 100m, volatilityPercent: 2.0m, isUptrend: true);
        var badBars = CreateShortBarsList(50); // Only 50 bars, need 200 for SMA200

        SetupMarketDataResponse("GOOD_DATA", goodBars);
        SetupMarketDataResponse("BAD_DATA", badBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert
        signalList.Should().HaveCount(1);
        signalList.Should().Contain(s => s.Symbol == "GOOD_DATA");
        signalList.Should().NotContain(s => s.Symbol == "BAD_DATA");
    }

    [Fact]
    public async Task RunAsync_WithZeroOrNegativeValues_FiltersThemOut()
    {
        // Arrange
        var symbols = new[] { "ZERO_PRICE", "VALID" };
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Create bars with zero price (all bars have 0 close price)
        var zeroPriceBars = CreateBarsWithZeroPrice("ZERO_PRICE");

        // Valid bars
        var validBars = CreateBarsWithVolatility("VALID", price: 100m, volatilityPercent: 2.0m, isUptrend: true);

        SetupMarketDataResponse("ZERO_PRICE", zeroPriceBars);
        SetupMarketDataResponse("VALID", validBars);

        // Act
        var signals = await _signalGenerator.RunAsync(10);
        var signalList = signals.ToList();

        // Assert - Only valid should pass (zero price should be filtered)
        signalList.Should().HaveCount(1);
        signalList.Should().Contain(s => s.Symbol == "VALID");
        signalList.Should().NotContain(s => s.Symbol == "ZERO_PRICE");

        // Verify the valid signal has positive price and ATR
        var validSignal = signalList.First(s => s.Symbol == "VALID");
        validSignal.Price.Should().BeGreaterThan(0);
        validSignal.Atr.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task RunAsync_WithException_ReturnsEmptyList()
    {
        // Arrange
        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ThrowsAsync(new Exception("Universe provider error"));

        // Act
        var signals = await _signalGenerator.RunAsync(10);

        // Assert
        signals.Should().BeEmpty();
    }

    [Fact]
    [TestTimeout(TestTimeouts.Unit)]
    public async Task RunAsync_ConcentratesCapitalInLeaders_RanksAndLimitsCorrectly()
    {
        // Arrange - Create a smaller universe of 8 symbols for faster testing
        var symbols = new[]
        {
            "LEADER1", "LEADER2",     // Top performers (25%, 22%)
            "STRONG1", "STRONG2",     // Strong performers (18%, 16%)
            "GOOD1", "GOOD2",         // Good performers (12%, 10%)
            "WEAK1", "WEAK2"          // Weak performers (4%, 2%)
        };

        _mockUniverseProvider.Setup(x => x.GetSymbolsAsync()).ReturnsAsync(symbols);

        // Create bars with specific 6-month returns (all pass SMA and volatility filters)
        var symbolReturns = new Dictionary<string, decimal>
        {
            ["LEADER1"] = 0.25m,   ["LEADER2"] = 0.22m,
            ["STRONG1"] = 0.18m,   ["STRONG2"] = 0.16m,
            ["GOOD1"] = 0.12m,     ["GOOD2"] = 0.10m,
            ["WEAK1"] = 0.04m,     ["WEAK2"] = 0.02m
        };

        foreach (var kvp in symbolReturns)
        {
            var finalPrice = 100m * (1 + kvp.Value); // Calculate final price based on return
            var bars = CreateBarsWithReturn(kvp.Key, finalPrice, 100m, 2.0m);
            SetupMarketDataResponse(kvp.Key, bars);
        }

        // Act - Request top 5 signals (concentrate capital in leaders)
        var signals = await _signalGenerator.RunAsync(5);
        var signalList = signals.ToList();

        // Assert - Should return exactly 5 signals (capital concentration)
        signalList.Should().HaveCount(5, "Should concentrate capital in top 5 performers only");

        // Verify that leaders are included (top performers)
        signalList.Should().Contain(s => s.Symbol == "LEADER1");
        signalList.Should().Contain(s => s.Symbol == "LEADER2");

        // Verify that weaker performers are excluded (capital concentration)
        signalList.Should().NotContain(s => s.Symbol.StartsWith("WEAK"),
            "Weak performers should be excluded to concentrate capital");

        // Verify all signals have positive returns (top performers only)
        signalList.Should().OnlyContain(s => s.SixMonthReturn > 0,
            "All selected signals should have positive returns");

        // Verify returns are in descending order (most important test for ranking)
        for (int i = 0; i < signalList.Count - 1; i++)
        {
            signalList[i].SixMonthReturn.Should().BeGreaterOrEqualTo(signalList[i + 1].SixMonthReturn,
                $"Signal at index {i} should have higher or equal return than signal at index {i + 1}");
        }

        // Verify that the first signal has higher return than the last (proper ranking)
        signalList[0].SixMonthReturn.Should().BeGreaterThan(signalList[4].SixMonthReturn,
            "The top performer should have significantly higher return than the 5th performer");

        // Verify that LEADER1 is in the top 2 (should be one of the best performers)
        var topTwo = signalList.Take(2).Select(s => s.Symbol).ToList();
        topTwo.Should().Contain("LEADER1", "LEADER1 should be in top 2 performers");
    }

    private void SetupMarketDataResponse(string symbol, List<IBar> bars)
    {
        var mockResponse = new Mock<IPage<IBar>>();
        mockResponse.Setup(x => x.Items).Returns(bars);
        
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockResponse.Object);
    }

    private List<IBar> CreateMockBars(string symbol, decimal price, decimal sma50, decimal sma200, decimal atr)
    {
        return CreateMockBarsWithReturn(symbol, price, sma50, sma200, atr, 0.10m); // Default 10% return
    }

    private List<IBar> CreateMockBarsWithReturn(string symbol, decimal price, decimal sma50, decimal sma200, decimal atr, decimal sixMonthReturn)
    {
        var bars = new List<IBar>();
        var startPrice = price / (1 + sixMonthReturn); // Calculate start price for desired return

        // Create 250 bars with realistic price data that will produce expected SMA values
        for (int i = 0; i < 250; i++)
        {
            var mockBar = new Mock<IBar>();
            decimal currentPrice;

            // For the last 50 bars, ensure they average to sma50
            if (i >= 200)
            {
                currentPrice = sma50 + (price - sma50) * (i - 200) / 49m;
            }
            // For bars 50-199, create a trend that leads to sma200
            else if (i >= 50)
            {
                currentPrice = sma200 + (sma50 - sma200) * (i - 50) / 149m;
            }
            // For first 50 bars, create base trend
            else
            {
                currentPrice = startPrice + (sma200 - startPrice) * i / 49m;
            }

            // Add some volatility to create realistic ATR
            var volatilityFactor = atr / price; // Target volatility as percentage
            var dailyRange = currentPrice * (decimal)volatilityFactor;

            mockBar.Setup(x => x.Close).Returns(currentPrice);
            mockBar.Setup(x => x.Open).Returns(currentPrice - dailyRange * 0.2m);
            mockBar.Setup(x => x.High).Returns(currentPrice + dailyRange * 0.6m);
            mockBar.Setup(x => x.Low).Returns(currentPrice - dailyRange * 0.6m);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-249 + i));

            bars.Add(mockBar.Object);
        }

        return bars;
    }

    private List<IBar> CreateShortBarsList(int count)
    {
        var bars = new List<IBar>();
        for (int i = 0; i < count; i++)
        {
            var mockBar = new Mock<IBar>();
            mockBar.Setup(x => x.Close).Returns(100m);
            mockBar.Setup(x => x.Open).Returns(99m);
            mockBar.Setup(x => x.High).Returns(101m);
            mockBar.Setup(x => x.Low).Returns(98m);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-count + i));
            bars.Add(mockBar.Object);
        }
        return bars;
    }

    /// <summary>
    /// Creates bars with specific volatility characteristics and trend direction
    /// </summary>
    /// <param name="symbol">Symbol name</param>
    /// <param name="price">Final price</param>
    /// <param name="volatilityPercent">Target volatility as percentage (e.g., 2.5 for 2.5%)</param>
    /// <param name="isUptrend">True for uptrend, false for downtrend, null for sideways</param>
    /// <returns>List of bars with realistic price data</returns>
    private List<IBar> CreateBarsWithVolatility(string symbol, decimal price, decimal volatilityPercent, bool? isUptrend)
    {
        var bars = new List<IBar>();
        var random = new Random(symbol.GetHashCode()); // Deterministic randomness based on symbol

        // Calculate starting price based on trend
        decimal startPrice = isUptrend switch
        {
            true => price * 0.7m,   // Strong uptrend from 70% to 100%
            false => price * 1.3m,  // Downtrend from 130% to 100%
            null => price * 0.95m   // Sideways from 95% to 100%
        };

        for (int i = 0; i < 250; i++)
        {
            var mockBar = new Mock<IBar>();

            // Calculate trend component
            decimal trendPrice = isUptrend switch
            {
                true => startPrice + (price - startPrice) * i / 249m,
                false => startPrice - (startPrice - price) * i / 249m,
                null => startPrice + (price - startPrice) * (decimal)Math.Sin(i * Math.PI / 50) * 0.5m // Sideways oscillation
            };

            // Add volatility
            var dailyVolatility = trendPrice * volatilityPercent / 100m;
            var randomFactor = (decimal)(random.NextDouble() - 0.5) * 2; // -1 to 1

            var open = trendPrice + randomFactor * dailyVolatility * 0.3m;
            var close = trendPrice + randomFactor * dailyVolatility * 0.2m;
            var high = Math.Max(open, close) + dailyVolatility * 0.7m;
            var low = Math.Min(open, close) - dailyVolatility * 0.7m;

            mockBar.Setup(x => x.Close).Returns(close);
            mockBar.Setup(x => x.Open).Returns(open);
            mockBar.Setup(x => x.High).Returns(high);
            mockBar.Setup(x => x.Low).Returns(low);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-249 + i));

            bars.Add(mockBar.Object);
        }

        return bars;
    }

    /// <summary>
    /// Creates bars with specific return characteristics
    /// </summary>
    private List<IBar> CreateBarsWithReturn(string symbol, decimal finalPrice, decimal startPrice, decimal volatilityPercent)
    {
        // Use the optimized test data factory for much faster test execution
        return TestDataFactory.CreateReturnBars(symbol, startPrice, finalPrice, TestConstants.MinimalBarCount);
    }

    /// <summary>
    /// Creates bars with zero price for testing price filtering
    /// </summary>
    private List<IBar> CreateBarsWithZeroPrice(string symbol)
    {
        // Use minimal bars with zero price for filter testing
        return TestDataFactory.CreateMinimalBars(symbol, TestConstants.MinimalBarCount, basePrice: 0m);
    }
}
